package com.investment.integration

import com.investment.database.DatabaseManager
import com.investment.model.OHLCV
import com.investment.service.DMIService
import com.investment.api.model.DMIRequest
import com.investment.api.model.DMIResponse
import spock.lang.Specification
import spock.lang.Shared

import java.time.LocalDate

/**
 * Integration test to verify that INCREMENTAL and FULL_RECALCULATION modes
 * produce consistent DMI results, addressing the specific issue where
 * incremental calculations were producing different values.
 */
class DMIIncrementalConsistencySpec extends Specification {

    @Shared
    DatabaseManager databaseManager

    def setupSpec() {
        databaseManager = new DatabaseManager()
        databaseManager.initDatabase()
    }

    def cleanupSpec() {
        databaseManager.closeConnection()
    }

    def "should produce consistent results between INCREMENTAL and FULL_RECALCULATION modes"() {
        given: "a symbol with realistic MSFT-like data"
        String symbol = "MSFT_CONSISTENCY_TEST"
        databaseManager.saveInstrument(symbol, "Microsoft Consistency Test", "Technology")

        and: "historical OHLCV data similar to the user's example"
        List<OHLCV> historicalData = createMSFTLikeData(symbol)
        databaseManager.saveOHLCVData(historicalData)

        when: "performing initial full DMI calculation using hybrid method"
        int fullRecordsUpdated = databaseManager.calculateAndUpdateDMIHybrid(symbol, 14, false)

        and: "capturing the full calculation results"
        List<Map<String, Object>> fullResults = getDMIResults(symbol)

        and: "clearing DMI data and performing incremental calculation"
        databaseManager.clearDMIData(symbol)

        // First, calculate some initial data (simulate existing DMI data)
        databaseManager.calculateAndUpdateDMIHybrid(symbol, 14, false)

        // Now perform incremental calculation
        int incrementalRecordsUpdated = databaseManager.calculateAndUpdateDMIHybridIncremental(symbol, 14, false)

        and: "capturing the incremental calculation results"
        List<Map<String, Object>> incrementalResults = getDMIResults(symbol)

        then: "both calculations should succeed"
        fullRecordsUpdated > 0
        incrementalRecordsUpdated >= 0  // May be 0 if no new data to process
        
        and: "the results should be identical"
        fullResults.size() == incrementalResults.size()
        
        and: "each DMI value should match between the two methods"
        for (int i = 0; i < fullResults.size(); i++) {
            Map<String, Object> fullRow = fullResults[i]
            Map<String, Object> incrementalRow = incrementalResults[i]
            
            assert fullRow.date == incrementalRow.date
            
            // Compare DMI values with tolerance for floating point precision
            if (fullRow.dmi_plus_di != null && incrementalRow.dmi_plus_di != null) {
                assert Math.abs((Double)fullRow.dmi_plus_di - (Double)incrementalRow.dmi_plus_di) < 0.001
            } else {
                assert fullRow.dmi_plus_di == incrementalRow.dmi_plus_di
            }
            
            if (fullRow.dmi_minus_di != null && incrementalRow.dmi_minus_di != null) {
                assert Math.abs((Double)fullRow.dmi_minus_di - (Double)incrementalRow.dmi_minus_di) < 0.001
            } else {
                assert fullRow.dmi_minus_di == incrementalRow.dmi_minus_di
            }
            
            if (fullRow.dmi_dx != null && incrementalRow.dmi_dx != null) {
                assert Math.abs((Double)fullRow.dmi_dx - (Double)incrementalRow.dmi_dx) < 0.001
            } else {
                assert fullRow.dmi_dx == incrementalRow.dmi_dx
            }
            
            if (fullRow.dmi_adx != null && incrementalRow.dmi_adx != null) {
                assert Math.abs((Double)fullRow.dmi_adx - (Double)incrementalRow.dmi_adx) < 0.001
            } else {
                assert fullRow.dmi_adx == incrementalRow.dmi_adx
            }
        }
        
        and: "specifically check the most recent dates that were problematic in the user's example"
        Map<String, Object> latestFull = fullResults.find { it.date.toString() == "2025-06-11" }
        Map<String, Object> latestIncremental = incrementalResults.find { it.date.toString() == "2025-06-11" }
        
        if (latestFull && latestIncremental) {
            assert Math.abs((Double)latestFull.dmi_plus_di - (Double)latestIncremental.dmi_plus_di) < 0.001
            assert Math.abs((Double)latestFull.dmi_minus_di - (Double)latestIncremental.dmi_minus_di) < 0.001
            if (latestFull.dmi_adx != null && latestIncremental.dmi_adx != null) {
                assert Math.abs((Double)latestFull.dmi_adx - (Double)latestIncremental.dmi_adx) < 0.001
            }
        }
    }
    
    private List<OHLCV> createMSFTLikeData(String symbol) {
        List<OHLCV> data = []
        
        // Create data similar to the user's MSFT example
        LocalDate startDate = LocalDate.of(2025, 5, 1)
        double basePrice = 425.0
        
        // Generate 50 days of realistic price data
        for (int i = 0; i < 50; i++) {
            LocalDate date = startDate.plusDays(i)
            
            // Create realistic price movements with some volatility
            double dailyChange = (Math.random() - 0.5) * 6.0 // +/- 3% daily change
            basePrice = Math.max(basePrice + dailyChange, 400.0) // Minimum price
            
            double open = basePrice + (Math.random() - 0.5) * 2.0
            double close = basePrice + (Math.random() - 0.5) * 2.0
            double high = Math.max(open, close) + Math.random() * 3.0
            double low = Math.min(open, close) - Math.random() * 3.0
            long volume = (long) (15000000 + Math.random() * 20000000) // 15M-35M volume
            
            data.add(new OHLCV(symbol, date, open, high, low, close, volume))
        }
        
        return data
    }
    
    private List<Map<String, Object>> getDMIResults(String symbol) {
        String sql = """
            SELECT date, dmi_plus_di, dmi_minus_di, dmi_dx, dmi_adx 
            FROM ohlcv 
            WHERE symbol = ? 
            ORDER BY date
        """
        
        List<Map<String, Object>> results = []
        
        databaseManager.connection.prepareStatement(sql).withCloseable { stmt ->
            stmt.setString(1, symbol)
            stmt.executeQuery().withCloseable { rs ->
                while (rs.next()) {
                    results.add([
                        date: rs.getDate("date").toLocalDate(),
                        dmi_plus_di: rs.getObject("dmi_plus_di"),
                        dmi_minus_di: rs.getObject("dmi_minus_di"),
                        dmi_dx: rs.getObject("dmi_dx"),
                        dmi_adx: rs.getObject("dmi_adx")
                    ])
                }
            }
        }
        
        return results
    }
}

import React, { useState, useEffect, useCallback, useRef } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Box,
  Typography,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  CircularProgress,
  Alert,
  TextField,
  Button,
  Grid,
  Card,
  CardContent,
  Chip,
  Pagination,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  TableSortLabel,
  LinearProgress,
  ButtonBase,
  Tooltip,
  Checkbox,
  Snackbar,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Divider,
} from '@mui/material';
import {
  Search as SearchIcon,
  Refresh as RefreshIcon,
  Clear as ClearIcon,
  TrendingUp as TrendingUpIcon
} from '@mui/icons-material';

import { InstrumentService } from '../services/api/instrumentService';
import { OHLCVService } from '../services/api/ohlcvService';
import { Instrument, PaginatedResponse, BulkUpdateProgress, BulkUpdateResult } from '../types/api';
import BulkActionToolbar from '../components/BulkActionToolbar';
import BulkUpdateProgressDialog from '../components/BulkUpdateProgressDialog';

interface SortConfig {
  field: 'marketCap' | 'symbol' | 'name';
  direction: 'asc' | 'desc';
}

const Instruments: React.FC = () => {
  const navigate = useNavigate();

  // State management
  const [instruments, setInstruments] = useState<Instrument[]>([]);
  const [paginationData, setPaginationData] = useState<PaginatedResponse<Instrument> | null>(null);
  const [loading, setLoading] = useState(true);
  const [pageLoading, setPageLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Search and filtering
  const [searchQuery, setSearchQuery] = useState('');
  const [isSearchMode, setIsSearchMode] = useState(false);

  // Pagination and sorting
  const [currentPage, setCurrentPage] = useState(1); // 1-based for UI, 0-based for API
  const [pageSize, setPageSize] = useState(50);
  const [sortConfig, setSortConfig] = useState<SortConfig>({
    field: 'marketCap',
    direction: 'desc'
  });

  // Bulk operations state
  const [selectedSymbols, setSelectedSymbols] = useState<Set<string>>(new Set());
  const [showBulkToolbar, setShowBulkToolbar] = useState(false);
  const [bulkUpdateProgress, setBulkUpdateProgress] = useState<BulkUpdateProgress[]>([]);
  const [showProgressDialog, setShowProgressDialog] = useState(false);
  const [bulkUpdateResult, setBulkUpdateResult] = useState<BulkUpdateResult | null>(null);
  const [isBulkUpdateComplete, setIsBulkUpdateComplete] = useState(false);
  const [successMessage, setSuccessMessage] = useState<string | null>(null);

  // Abort controller for cancelling bulk operations
  const abortControllerRef = useRef<AbortController | null>(null);

  // Load instruments with current pagination and sorting
  const loadInstruments = useCallback(async (showPageLoading = false) => {
    try {
      if (showPageLoading) {
        setPageLoading(true);
      } else {
        setLoading(true);
      }
      setError(null);

      const apiPage = currentPage - 1; // Convert to 0-based for API
      const response = await InstrumentService.getInstruments(
        apiPage,
        pageSize,
        sortConfig.field,
        sortConfig.direction
      );

      if (response.success && response.data) {
        setInstruments(response.data.content);
        setPaginationData(response.data);
        setIsSearchMode(false);
      } else {
        setError(response.message || 'Failed to load instruments');
      }
    } catch (err: any) {
      console.error('Error loading instruments:', err);
      setError('Failed to load instruments');
    } finally {
      setLoading(false);
      setPageLoading(false);
    }
  }, [currentPage, pageSize, sortConfig]);

  // Initial load
  useEffect(() => {
    loadInstruments();
  }, [loadInstruments]);

  // Search functionality
  const handleSearch = async () => {
    if (!searchQuery.trim()) {
      // Clear search and return to paginated view
      setIsSearchMode(false);
      setCurrentPage(1);
      loadInstruments();
      return;
    }

    try {
      setLoading(true);
      setError(null);

      const response = await InstrumentService.searchInstruments(searchQuery);
      if (response.success && response.data) {
        setInstruments(response.data);
        setIsSearchMode(true);
        setPaginationData(null); // Clear pagination for search results
      } else {
        setError(response.message || 'Failed to search instruments');
      }
    } catch (err: any) {
      console.error('Error searching instruments:', err);
      setError('Failed to search instruments');
    } finally {
      setLoading(false);
    }
  };

  // Clear search and return to paginated view
  const handleClearSearch = () => {
    setSearchQuery('');
    setIsSearchMode(false);
    setCurrentPage(1);
    // Clear selections when switching between search and pagination modes
    setSelectedSymbols(new Set());
    setShowBulkToolbar(false);
    loadInstruments();
  };

  // Handle page change
  const handlePageChange = (event: React.ChangeEvent<unknown>, page: number) => {
    setCurrentPage(page);
    loadInstruments(true); // Show page loading indicator
    // Note: selectedSymbols state is preserved across page changes
  };

  // Handle page size change
  const handlePageSizeChange = (event: any) => {
    const newSize = parseInt(event.target.value);
    setPageSize(newSize);
    setCurrentPage(1); // Reset to first page
  };

  // Handle sorting
  const handleSort = (field: 'marketCap' | 'symbol' | 'name') => {
    const newDirection = sortConfig.field === field && sortConfig.direction === 'desc' ? 'asc' : 'desc';
    setSortConfig({ field, direction: newDirection });
    setCurrentPage(1); // Reset to first page when sorting changes
    // Note: selectedSymbols state is preserved across sorting changes
  };

  // Handle navigation to OHLCV page
  const handleInstrumentClick = (symbol: string) => {
    try {
      // Navigate to OHLCV page with symbol parameter
      navigate(`/ohlcv/${symbol.toUpperCase()}`);
    } catch (error) {
      console.error('Navigation error:', error);
      setError(`Failed to navigate to OHLCV data for ${symbol}`);
    }
  };

  // Bulk selection handlers
  const handleSelectSymbol = (symbol: string, checked: boolean) => {
    const newSelection = new Set(selectedSymbols);
    if (checked) {
      newSelection.add(symbol);
    } else {
      newSelection.delete(symbol);
    }
    setSelectedSymbols(newSelection);
    setShowBulkToolbar(newSelection.size > 0);
  };

  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      const allSymbols = new Set(instruments.map(instrument => instrument.symbol));
      setSelectedSymbols(allSymbols);
      setShowBulkToolbar(true);
    } else {
      setSelectedSymbols(new Set());
      setShowBulkToolbar(false);
    }
  };

  const handleClearSelection = () => {
    setSelectedSymbols(new Set());
    setShowBulkToolbar(false);
  };

  // Bulk OHLCV update handlers
  const handleBulkUpdateOHLCV = async () => {
    if (selectedSymbols.size === 0) return;

    const symbolsArray = Array.from(selectedSymbols);
    setBulkUpdateProgress(symbolsArray.map(symbol => ({ symbol, status: 'pending' })));
    setShowProgressDialog(true);
    setIsBulkUpdateComplete(false);
    setBulkUpdateResult(null);

    // Create abort controller for cancellation
    abortControllerRef.current = new AbortController();

    try {
      const result = await OHLCVService.bulkUpdateOHLCVData(
        { symbols: symbolsArray, dryRun: false },
        (progress) => setBulkUpdateProgress(progress),
        abortControllerRef.current.signal
      );

      setBulkUpdateResult(result);
      setIsBulkUpdateComplete(true);

      if (result.errorCount === 0) {
        setSuccessMessage(`Successfully updated OHLCV data for ${result.successCount} symbols`);
        handleClearSelection(); // Clear selection on complete success
      }
    } catch (error: any) {
      console.error('Bulk update error:', error);
      setError('Bulk update operation failed');
      setIsBulkUpdateComplete(true);
    }
  };

  const handleCancelBulkUpdate = () => {
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }
  };

  const handleRetryFailedSymbols = async () => {
    if (!bulkUpdateResult) return;

    const failedSymbols = bulkUpdateProgress.filter(p => p.status === 'error').map(p => p.symbol);
    if (failedSymbols.length === 0) return;

    setBulkUpdateProgress(failedSymbols.map(symbol => ({ symbol, status: 'pending' })));
    setIsBulkUpdateComplete(false);
    setBulkUpdateResult(null);

    abortControllerRef.current = new AbortController();

    try {
      const result = await OHLCVService.retryFailedSymbols(
        bulkUpdateProgress,
        { dryRun: false },
        (progress) => setBulkUpdateProgress(progress),
        abortControllerRef.current.signal
      );

      setBulkUpdateResult(result);
      setIsBulkUpdateComplete(true);

      if (result.errorCount === 0) {
        setSuccessMessage(`Successfully retried and updated ${result.successCount} symbols`);
      }
    } catch (error: any) {
      console.error('Retry failed symbols error:', error);
      setError('Retry operation failed');
      setIsBulkUpdateComplete(true);
    }
  };

  const handleCloseProgressDialog = () => {
    setShowProgressDialog(false);
    setBulkUpdateProgress([]);
    setBulkUpdateResult(null);
    setIsBulkUpdateComplete(false);
  };

  // Utility functions
  const formatMarketCap = (marketCap?: number) => {
    if (!marketCap) return 'N/A';

    if (marketCap >= 1e12) {
      return `$${(marketCap / 1e12).toFixed(2)}T`;
    } else if (marketCap >= 1e9) {
      return `$${(marketCap / 1e9).toFixed(2)}B`;
    } else if (marketCap >= 1e6) {
      return `$${(marketCap / 1e6).toFixed(2)}M`;
    } else {
      return `$${marketCap.toLocaleString()}`;
    }
  };

  const getPaginationInfo = () => {
    if (isSearchMode) {
      return `Showing ${instruments.length} search results`;
    }
    if (!paginationData) return '';

    const start = (currentPage - 1) * pageSize + 1;
    const end = Math.min(currentPage * pageSize, paginationData.totalElements);
    return `Showing ${start}-${end} of ${paginationData.totalElements.toLocaleString()} instruments`;
  };

  return (
    <Box>
      <Typography variant="h4" gutterBottom>
        Financial Instruments
      </Typography>

      {/* Search and Controls */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Grid container spacing={2} alignItems="center">
            {/* Search Field */}
            <Grid item xs={12} md={5}>
              <TextField
                fullWidth
                label="Search instruments"
                placeholder="Enter symbol or company name"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
                InputProps={{
                  startAdornment: <SearchIcon sx={{ mr: 1, color: 'text.secondary' }} />,
                }}
              />
            </Grid>

            {/* Search Actions */}
            <Grid item xs={12} md={2}>
              <Button
                variant="contained"
                onClick={handleSearch}
                fullWidth
                startIcon={<SearchIcon />}
                disabled={loading || pageLoading}
              >
                Search
              </Button>
            </Grid>

            <Grid item xs={12} md={2}>
              <Button
                variant="outlined"
                onClick={handleClearSearch}
                fullWidth
                startIcon={<ClearIcon />}
                disabled={loading || pageLoading}
              >
                Clear
              </Button>
            </Grid>

            <Grid item xs={12} md={2}>
              <Button
                variant="outlined"
                onClick={() => loadInstruments()}
                fullWidth
                startIcon={<RefreshIcon />}
                disabled={loading || pageLoading}
              >
                Refresh
              </Button>
            </Grid>

            {/* Page Size Selector */}
            {!isSearchMode && (
              <Grid item xs={12} md={1}>
                <FormControl fullWidth size="small">
                  <InputLabel>Size</InputLabel>
                  <Select
                    value={pageSize}
                    label="Size"
                    onChange={handlePageSizeChange}
                    disabled={loading || pageLoading}
                  >
                    <MenuItem value={25}>25</MenuItem>
                    <MenuItem value={50}>50</MenuItem>
                    <MenuItem value={100}>100</MenuItem>
                  </Select>
                </FormControl>
              </Grid>
            )}
          </Grid>

          {/* Pagination Info */}
          <Box mt={2} display="flex" justifyContent="space-between" alignItems="center">
            <Typography variant="body2" color="text.secondary">
              {getPaginationInfo()}
            </Typography>
            {(loading || pageLoading) && (
              <Typography variant="body2" color="primary">
                {pageLoading ? 'Loading page...' : 'Loading...'}
              </Typography>
            )}
          </Box>
        </CardContent>
      </Card>

      {/* Error Alert */}
      {error && (
        <Alert severity="error" sx={{ mb: 3 }} onClose={() => setError(null)}>
          {error}
        </Alert>
      )}

      {/* Page Loading Indicator */}
      {pageLoading && (
        <Box sx={{ mb: 2 }}>
          <LinearProgress />
        </Box>
      )}

      {/* Main Loading */}
      {loading && !pageLoading && (
        <Box display="flex" justifyContent="center" p={3}>
          <CircularProgress />
        </Box>
      )}

      {/* Instruments Table */}
      {!loading && (
        <Paper>
          <TableContainer>
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell padding="checkbox">
                    <Checkbox
                      indeterminate={selectedSymbols.size > 0 && selectedSymbols.size < instruments.length}
                      checked={instruments.length > 0 && selectedSymbols.size === instruments.length}
                      onChange={(e) => handleSelectAll(e.target.checked)}
                      disabled={instruments.length === 0}
                    />
                  </TableCell>
                  <TableCell>
                    <TableSortLabel
                      active={sortConfig.field === 'symbol'}
                      direction={sortConfig.field === 'symbol' ? sortConfig.direction : 'asc'}
                      onClick={() => handleSort('symbol')}
                      disabled={isSearchMode}
                    >
                      <strong>Symbol</strong>
                    </TableSortLabel>
                  </TableCell>
                  <TableCell>
                    <TableSortLabel
                      active={sortConfig.field === 'name'}
                      direction={sortConfig.field === 'name' ? sortConfig.direction : 'asc'}
                      onClick={() => handleSort('name')}
                      disabled={isSearchMode}
                    >
                      <strong>Company Name</strong>
                    </TableSortLabel>
                  </TableCell>
                  <TableCell><strong>Type</strong></TableCell>
                  <TableCell><strong>Sector</strong></TableCell>
                  <TableCell><strong>Industry</strong></TableCell>
                  <TableCell align="right">
                    <TableSortLabel
                      active={sortConfig.field === 'marketCap'}
                      direction={sortConfig.field === 'marketCap' ? sortConfig.direction : 'desc'}
                      onClick={() => handleSort('marketCap')}
                      disabled={isSearchMode}
                    >
                      <strong>Market Cap</strong>
                    </TableSortLabel>
                  </TableCell>
                  <TableCell><strong>Country</strong></TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {instruments.map((instrument) => (
                  <TableRow
                    key={instrument.symbol}
                    hover
                    selected={selectedSymbols.has(instrument.symbol)}
                  >
                    <TableCell padding="checkbox">
                      <Checkbox
                        checked={selectedSymbols.has(instrument.symbol)}
                        onChange={(e) => handleSelectSymbol(instrument.symbol, e.target.checked)}
                      />
                    </TableCell>
                    <TableCell>
                      <Tooltip title={`View OHLCV data for ${instrument.symbol}`} arrow>
                        <ButtonBase
                          onClick={() => handleInstrumentClick(instrument.symbol)}
                          sx={{
                            borderRadius: 1,
                            transition: 'all 0.2s ease-in-out',
                            '&:hover': {
                              transform: 'scale(1.05)',
                            },
                          }}
                        >
                          <Chip
                            label={instrument.symbol}
                            variant="outlined"
                            size="small"
                            clickable
                            icon={<TrendingUpIcon />}
                            sx={{
                              fontWeight: 600,
                              cursor: 'pointer',
                              '&:hover': {
                                backgroundColor: 'primary.light',
                                color: 'primary.contrastText',
                                borderColor: 'primary.main',
                              },
                            }}
                          />
                        </ButtonBase>
                      </Tooltip>
                    </TableCell>
                    <TableCell>
                      <Tooltip title={`View OHLCV data for ${instrument.symbol}`} arrow>
                        <ButtonBase
                          onClick={() => handleInstrumentClick(instrument.symbol)}
                          sx={{
                            textAlign: 'left',
                            borderRadius: 1,
                            padding: '4px 8px',
                            transition: 'all 0.2s ease-in-out',
                            '&:hover': {
                              backgroundColor: 'action.hover',
                              transform: 'translateX(4px)',
                            },
                          }}
                        >
                          <Typography
                            variant="body2"
                            sx={{
                              cursor: 'pointer',
                              color: 'primary.main',
                              fontWeight: 500,
                              '&:hover': {
                                textDecoration: 'underline',
                              },
                            }}
                          >
                            {instrument.name}
                          </Typography>
                        </ButtonBase>
                      </Tooltip>
                    </TableCell>
                    <TableCell>{instrument.type}</TableCell>
                    <TableCell>{instrument.sector || 'N/A'}</TableCell>
                    <TableCell>{instrument.industry || 'N/A'}</TableCell>
                    <TableCell align="right">
                      {formatMarketCap(instrument.marketCap)}
                    </TableCell>
                    <TableCell>
                      {instrument.country || 'N/A'}
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>

          {/* Pagination Controls */}
          {!isSearchMode && paginationData && paginationData.totalPages > 1 && (
            <Box
              display="flex"
              justifyContent="center"
              alignItems="center"
              p={2}
              borderTop="1px solid"
              borderColor="divider"
            >
              <Pagination
                count={paginationData.totalPages}
                page={currentPage}
                onChange={handlePageChange}
                color="primary"
                size="large"
                showFirstButton
                showLastButton
                disabled={pageLoading}
              />
            </Box>
          )}
        </Paper>
      )}

      {/* No Results */}
      {!loading && instruments.length === 0 && (
        <Paper>
          <Box textAlign="center" p={4}>
            <Typography variant="h6" color="text.secondary" gutterBottom>
              {isSearchMode ? 'No search results found' : 'No instruments found'}
            </Typography>
            <Typography variant="body2" color="text.secondary">
              {isSearchMode
                ? 'Try adjusting your search criteria or clear the search to view all instruments'
                : 'Try refreshing the data or check your connection'
              }
            </Typography>
            {isSearchMode && (
              <Button
                variant="outlined"
                onClick={handleClearSearch}
                sx={{ mt: 2 }}
                startIcon={<ClearIcon />}
              >
                Clear Search
              </Button>
            )}
          </Box>
        </Paper>
      )}

      {/* Bulk Action Toolbar */}
      <BulkActionToolbar
        selectedCount={selectedSymbols.size}
        onUpdateOHLCV={handleBulkUpdateOHLCV}
        onClearSelection={handleClearSelection}
        onClose={() => setShowBulkToolbar(false)}
        isVisible={showBulkToolbar}
        isLoading={showProgressDialog && !isBulkUpdateComplete}
      />

      {/* Bulk Update Progress Dialog */}
      <BulkUpdateProgressDialog
        open={showProgressDialog}
        onClose={handleCloseProgressDialog}
        onCancel={handleCancelBulkUpdate}
        onRetryFailed={handleRetryFailedSymbols}
        progress={bulkUpdateProgress}
        isComplete={isBulkUpdateComplete}
        result={bulkUpdateResult || undefined}
        title="Bulk OHLCV Data Update"
      />

      {/* Success Snackbar */}
      <Snackbar
        open={!!successMessage}
        autoHideDuration={6000}
        onClose={() => setSuccessMessage(null)}
        message={successMessage}
        anchorOrigin={{ vertical: 'top', horizontal: 'center' }}
      />
    </Box>
  );
};

export default Instruments;

import { get, post } from './apiClient';
import {
  Instrument,
  CreateInstrumentRequest,
  CsvUploadResponse,
  SyncResponse,
  ValidationRequest,
  ApiResponse,
  PaginatedResponse
} from '../../types/api';

export class InstrumentService {
  
  /**
   * Get all instruments with pagination
   */
  static async getInstruments(
    page: number = 0,
    size: number = 50,
    sortBy: string = 'marketCap',
    sortDir: string = 'desc'
  ): Promise<ApiResponse<PaginatedResponse<Instrument>>> {
    return get<PaginatedResponse<Instrument>>(`/instruments?page=${page}&size=${size}&sortBy=${sortBy}&sortDir=${sortDir}`);
  }

  /**
   * Get instrument by symbol
   */
  static async getInstrumentBySymbol(symbol: string): Promise<ApiResponse<Instrument>> {
    return get<Instrument>(`/instruments/${symbol}`);
  }

  /**
   * Search instruments by name or symbol
   */
  static async searchInstruments(query: string): Promise<ApiResponse<Instrument[]>> {
    return get<Instrument[]>(`/instruments/search?q=${encodeURIComponent(query)}`);
  }

  /**
   * Synchronize with SEC data
   */
  static async syncWithSec(
    dryRun: boolean = true,
    maxInstruments: number = 1000
  ): Promise<ApiResponse<SyncResponse>> {
    return post<SyncResponse>('/instruments/sync-sec-data', {
      dryRun,
      forceRefresh: false,
      maxInstruments
    });
  }

  /**
   * Upload CSV file with instruments
   */
  static async uploadCsv(
    file: File,
    dryRun: boolean = true,
    maxInstruments: number = 1000,
    skipDuplicates: boolean = true,
    validateData: boolean = true
  ): Promise<ApiResponse<CsvUploadResponse>> {
    const formData = new FormData();
    formData.append('file', file);
    formData.append('dryRun', dryRun.toString());
    formData.append('maxInstruments', maxInstruments.toString());
    formData.append('skipDuplicates', skipDuplicates.toString());
    formData.append('validateData', validateData.toString());

    return post<CsvUploadResponse>('/instruments/upload-csv', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
  }

  /**
   * Validate symbols against SEC data
   */
  static async validateSymbols(request: ValidationRequest): Promise<ApiResponse<any>> {
    return post<any>('/instruments/validate-symbols', request);
  }

  /**
   * Get instrument statistics
   */
  static async getStatistics(): Promise<ApiResponse<any>> {
    return get<any>('/instruments/statistics');
  }
}
